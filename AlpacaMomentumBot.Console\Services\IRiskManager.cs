namespace AlpacaMomentumBot.Console.Services;

public interface IRiskManager
{
    Task<RiskAssessment> AssessRiskAsync(TradingSignal signal, decimal portfolioValue, CancellationToken cancellationToken = default);
    Task<decimal> CalculateQuantityAsync(decimal price, double atr14, CancellationToken cancellationToken = default);
}

public record RiskAssessment(
    bool IsApproved,
    decimal MaxPositionSize,
    decimal StopLossPrice,
    decimal TakeProfitPrice,
    string RiskReason);
