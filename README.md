# AlpacaMomentumBot

A .NET 8 console application that implements an SMA-following momentum trading strategy using the Alpaca Markets API.

## Features

- **Scheduled Trading**: Automatically executes trading cycles at 16:10 ET on weekdays using Cronos
- **SMA Momentum Strategy**: Uses 20-day and 50-day Simple Moving Averages to generate trading signals
- **Risk Management**: Built-in position sizing, stop-loss, and take-profit mechanisms
- **Portfolio Management**: Checks account status, buying power, and position limits
- **Comprehensive Logging**: Uses Serilog for console and file logging with daily rolling logs
- **Dependency Injection**: Clean architecture with scoped services for each trading cycle

## Architecture

The application follows a clean architecture pattern with the following services:

- **ScheduleService**: IHostedService that triggers trading cycles using Cronos
- **TradingService**: Orchestrates the complete trading cycle
- **SignalGenerator**: Generates buy/sell signals using SMA momentum strategy
- **RiskManager**: Assesses risk and calculates position sizing
- **PortfolioGate**: Validates trading constraints and account status
- **TradeExecutor**: Executes trades and manages bracket orders
- **AlpacaClientFactory**: Creates and manages Alpaca API clients

## Setup

### Prerequisites

- .NET 8 SDK
- Alpaca Markets account (paper or live)

### Configuration

1. Copy `.env.example` to `.env`:
   ```bash
   cp AlpacaMomentumBot.Console/.env.example AlpacaMomentumBot.Console/.env
   ```

2. Update the `.env` file with your Alpaca credentials:
   ```
   ALPACA_API_KEY=your_alpaca_api_key_here
   ALPACA_SECRET_KEY=your_alpaca_secret_key_here
   ALPACA_PAPER=true
   ```

### Running the Application

```bash
# Build the solution
dotnet build AlpacaMomentumBot.sln

# Run the application
dotnet run --project AlpacaMomentumBot.Console

# Run tests
dotnet test AlpacaMomentumBot.sln
```

## Trading Strategy

The bot implements a simple SMA momentum strategy:

- **Buy Signal**: Price > SMA20 > SMA50 (bullish momentum)
- **Sell Signal**: Price < SMA20 < SMA50 (bearish momentum)
- **No Signal**: Mixed or unclear momentum

### Risk Management

- Maximum 5% of portfolio per position
- 2% stop-loss on each trade
- 6% take-profit target (3:1 risk/reward ratio)
- Maximum 10 open positions
- Minimum signal confidence of 30%

### Default Symbols

The bot monitors these symbols by default:
- SPY (S&P 500 ETF)
- QQQ (NASDAQ ETF)
- AAPL (Apple)
- MSFT (Microsoft)
- GOOGL (Google)

## Logging

Logs are written to:
- Console (real-time)
- `logs/alpaca-momentum-bot-YYYY-MM-DD.log` (daily rolling files, 30-day retention)

## Schedule

The bot runs automatically at 16:10 ET (4:10 PM Eastern Time) on weekdays, which is 10 minutes after the US market close. This timing allows for:
- End-of-day price data to be available
- Calculation of updated technical indicators
- Preparation for the next trading day

## Safety Features

- **Paper Trading**: Set `ALPACA_PAPER=true` for safe testing
- **Position Limits**: Prevents over-concentration
- **Market Hours Check**: Only trades when markets are open
- **Error Handling**: Comprehensive exception handling and logging
- **Dry Run Capability**: Can be easily modified for simulation mode

## Development

### Project Structure

```
AlpacaMomentumBot/
├── AlpacaMomentumBot.Console/          # Main console application
│   ├── Services/                       # Business logic services
│   ├── Program.cs                      # Application entry point
│   └── .env.example                    # Environment variables template
├── AlpacaMomentumBot.Tests/            # Unit tests
└── README.md                           # This file
```

### Adding New Features

1. Create interfaces in the `Services` folder
2. Implement services with proper dependency injection
3. Register services in `Program.cs`
4. Add comprehensive unit tests
5. Update documentation

## Disclaimer

This software is for educational and research purposes only. Trading involves substantial risk of loss and is not suitable for all investors. Past performance does not guarantee future results. Always test thoroughly with paper trading before using real money.

## License

This project is licensed under the MIT License.
