using Microsoft.Extensions.Logging;
using AlpacaMomentumBot.Console.Extensions;
using Alpaca.Markets;

namespace AlpacaMomentumBot.Console.Services;

public class PortfolioGate : IPortfolioGate
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly ILogger<PortfolioGate> _logger;

    public PortfolioGate(IAlpacaClientFactory clientFactory, ILogger<PortfolioGate> logger)
    {
        _clientFactory = clientFactory;
        _logger = logger;
    }

    public async Task<PortfolioStatus> GetPortfolioStatusAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting portfolio status");

            using var tradingClient = _clientFactory.CreateTradingClient();

            // Get account information
            var account = await tradingClient.GetAccountAsync(cancellationToken);
            
            // Get market status
            var clock = await tradingClient.GetClockAsync(cancellationToken);
            
            // Get open positions
            var positions = await tradingClient.ListPositionsAsync(cancellationToken);

            var status = new PortfolioStatus(
                TotalValue: account.Equity,
                AvailableCash: account.TradableCash,
                DayTradingBuyingPower: account.DayTradingBuyingPower,
                OpenPositions: positions.Count,
                IsMarketOpen: clock.IsOpen
            );

            _logger.LogInformation("Portfolio Status - Value: {Value:C}, Cash: {Cash:C}, Positions: {Positions}, Market Open: {IsOpen}",
                status.TotalValue, status.AvailableCash, status.OpenPositions, status.IsMarketOpen);

            return status;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting portfolio status");
            throw;
        }
    }

    public async Task<bool> CanExecuteTradeAsync(TradingSignal signal, RiskAssessment risk, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Checking if trade can be executed for {Symbol}", signal.Symbol);

            if (!risk.IsApproved)
            {
                _logger.LogWarning("Trade rejected by risk assessment: {Reason}", risk.RiskReason);
                return false;
            }

            var portfolioStatus = await GetPortfolioStatusAsync(cancellationToken);

            if (!portfolioStatus.IsMarketOpen)
            {
                _logger.LogWarning("Market is closed, cannot execute trade");
                return false;
            }

            // Check if we have sufficient buying power
            var estimatedTradeValue = risk.MaxPositionSize * 100m; // Placeholder price
            var availableBuyingPower = portfolioStatus.DayTradingBuyingPower ?? 0m;
            if (estimatedTradeValue > availableBuyingPower)
            {
                _logger.LogWarning("Insufficient buying power. Need: {Need:C}, Available: {Available:C}",
                    estimatedTradeValue, availableBuyingPower);
                return false;
            }

            // Check position limits (e.g., max 10 open positions)
            if (portfolioStatus.OpenPositions >= 10)
            {
                _logger.LogWarning("Too many open positions: {Count}", portfolioStatus.OpenPositions);
                return false;
            }

            using var tradingClient = _clientFactory.CreateTradingClient();
            
            // Check if we already have a position in this symbol
            try
            {
                var existingPosition = await tradingClient.GetPositionAsync(signal.Symbol, cancellationToken);
                if (existingPosition != null && Math.Abs(existingPosition.Quantity) > 0)
                {
                    _logger.LogWarning("Already have position in {Symbol}: {Quantity} shares",
                        signal.Symbol, existingPosition.Quantity);
                    return false;
                }
            }
            catch (Alpaca.Markets.RestClientErrorException)
            {
                // No existing position - this is fine, continue
                _logger.LogInformation("No existing position in {Symbol}, trade can proceed", signal.Symbol);
            }

            _logger.LogInformation("Trade approved for execution: {Symbol}", signal.Symbol);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if trade can be executed for {Symbol}", signal.Symbol);
            return false;
        }
    }

    public async Task<bool> ShouldTradeAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Checking if trading should be allowed based on SPY market conditions");

            using var dataClient = _clientFactory.CreateDataClient();

            // Get SPY historical data for SMA200 calculation (need at least 200 bars)
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddDays(-300); // Get extra days to ensure we have 200+ bars

            var request = new HistoricalBarsRequest("SPY", startTime, endTime, BarTimeFrame.Day);
            var barsPage = await dataClient.ListHistoricalBarsAsync(request, cancellationToken);
            var bars = barsPage.Items.ToList();

            if (bars.Count < 200)
            {
                _logger.LogWarning("Insufficient SPY data for SMA200 calculation. Got {Count} bars, need at least 200", bars.Count);
                return false; // Conservative approach - don't trade if we can't calculate SMA200
            }

            // Calculate SPY SMA200
            var spySma200 = bars.GetSma200();
            var spyClose = (double)bars.Last().Close;

            var shouldTrade = spyClose >= spySma200;

            _logger.LogInformation("SPY market condition check - Close: {Close:F2}, SMA200: {Sma200:F2}, Should Trade: {ShouldTrade}",
                spyClose, spySma200, shouldTrade);

            return shouldTrade;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking SPY market conditions");
            return false; // Conservative approach - don't trade on errors
        }
    }
}
