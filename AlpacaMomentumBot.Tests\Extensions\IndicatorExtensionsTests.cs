using AlpacaMomentumBot.Console.Extensions;
using Alpaca.Markets;
using FluentAssertions;
using Moq;
using Skender.Stock.Indicators;

namespace AlpacaMomentumBot.Tests.Extensions;

public class IndicatorExtensionsTests
{
    private readonly List<IBar> _sampleBars;

    public IndicatorExtensionsTests()
    {
        // Create sample data with known values for testing
        _sampleBars = CreateSampleBars();
    }

    [Fact]
    public void ToQuotes_WithValidBars_ShouldConvertCorrectly()
    {
        // Act
        var quotes = _sampleBars.ToQuotes();

        // Assert
        quotes.Should().HaveCount(_sampleBars.Count);
        quotes.Should().BeInAscendingOrder(q => q.Date);
        
        var firstQuote = quotes.First();
        var firstBar = _sampleBars.First();
        firstQuote.Date.Should().Be(firstBar.TimeUtc);
        firstQuote.Open.Should().Be(firstBar.Open);
        firstQuote.High.Should().Be(firstBar.High);
        firstQuote.Low.Should().Be(firstBar.Low);
        firstQuote.Close.Should().Be(firstBar.Close);
        firstQuote.Volume.Should().Be(firstBar.Volume);
    }

    [Fact]
    public void ToQuotes_WithNullBars_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => ((IEnumerable<IBar>)null!).ToQuotes();
        act.Should().Throw<ArgumentNullException>().WithParameterName("bars");
    }

    [Fact]
    public void ToQuotes_WithEmptyBars_ShouldThrowArgumentException()
    {
        // Arrange
        var emptyBars = new List<IBar>();

        // Act & Assert
        var act = () => emptyBars.ToQuotes();
        act.Should().Throw<ArgumentException>().WithParameterName("bars")
            .WithMessage("Bars collection cannot be empty*");
    }

    [Fact]
    public void GetAtr14_WithSufficientData_ShouldReturnValidAtr()
    {
        // Arrange
        var bars = CreateBarsForAtrTest(20);

        // Act
        var atr = bars.GetAtr14();

        // Assert
        atr.Should().BeGreaterThan(0);
        atr.Should().BeLessThan(10); // Reasonable range for our test data
    }

    [Fact]
    public void GetAtr14_WithInsufficientData_ShouldThrowArgumentException()
    {
        // Arrange
        var insufficientBars = CreateBarsForAtrTest(13);

        // Act & Assert
        var act = () => insufficientBars.GetAtr14();
        act.Should().Throw<ArgumentException>().WithParameterName("bars")
            .WithMessage("Insufficient bars for ATR14 calculation. Got 13 bars, need at least 14*");
    }

    [Fact]
    public void GetSma50_WithSufficientData_ShouldReturnValidSma()
    {
        // Arrange
        var bars = CreateBarsForSmaTest(60); // 60 bars for SMA50

        // Act
        var sma50 = bars.GetSma50();

        // Assert
        sma50.Should().BeGreaterThan(0);
        // For our test data with prices around 100, SMA should be close to that
        sma50.Should().BeInRange(95, 105);
    }

    [Fact]
    public void GetSma50_WithInsufficientData_ShouldThrowArgumentException()
    {
        // Arrange
        var insufficientBars = CreateBarsForSmaTest(49);

        // Act & Assert
        var act = () => insufficientBars.GetSma50();
        act.Should().Throw<ArgumentException>().WithParameterName("bars")
            .WithMessage("Insufficient bars for SMA50 calculation. Got 49 bars, need at least 50*");
    }

    [Fact]
    public void GetSma200_WithSufficientData_ShouldReturnValidSma()
    {
        // Arrange
        var bars = CreateBarsForSmaTest(210); // 210 bars for SMA200

        // Act
        var sma200 = bars.GetSma200();

        // Assert
        sma200.Should().BeGreaterThan(0);
        // For our test data with prices around 100, SMA should be close to that
        sma200.Should().BeInRange(95, 105);
    }

    [Fact]
    public void GetSma200_WithInsufficientData_ShouldThrowArgumentException()
    {
        // Arrange
        var insufficientBars = CreateBarsForSmaTest(199);

        // Act & Assert
        var act = () => insufficientBars.GetSma200();
        act.Should().Throw<ArgumentException>().WithParameterName("bars")
            .WithMessage("Insufficient bars for SMA200 calculation. Got 199 bars, need at least 200*");
    }

    [Fact]
    public void GetTotalReturn_WithValidData_ShouldCalculateCorrectReturn()
    {
        // Arrange
        var bars = CreateBarsForReturnTest();
        var days = 5;

        // Act
        var totalReturn = bars.GetTotalReturn(days);

        // Assert
        // Based on our test data: start price 104 (5 days ago), end price 110 = ~5.77% return
        totalReturn.Should().BeApproximately(0.0577, 0.001);
    }

    [Fact]
    public void GetTotalReturn_WithInvalidDays_ShouldThrowArgumentException()
    {
        // Act & Assert
        var act = () => _sampleBars.GetTotalReturn(0);
        act.Should().Throw<ArgumentException>().WithParameterName("days")
            .WithMessage("Days must be greater than 0*");

        var act2 = () => _sampleBars.GetTotalReturn(-1);
        act2.Should().Throw<ArgumentException>().WithParameterName("days")
            .WithMessage("Days must be greater than 0*");
    }

    [Fact]
    public void GetTotalReturn_WithInsufficientData_ShouldThrowArgumentException()
    {
        // Arrange
        var insufficientBars = CreateBarsForSmaTest(5); // Only 5 bars
        var days = 5; // Need 6 bars for 5-day return

        // Act & Assert
        var act = () => insufficientBars.GetTotalReturn(days);
        act.Should().Throw<ArgumentException>().WithParameterName("bars")
            .WithMessage("Insufficient bars for 5-day return calculation. Got 5 bars, need at least 6*");
    }

    private List<IBar> CreateSampleBars()
    {
        var bars = new List<IBar>();
        var baseDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        for (int i = 0; i < 10; i++)
        {
            var bar = new Mock<IBar>();
            bar.Setup(b => b.TimeUtc).Returns(baseDate.AddDays(i));
            bar.Setup(b => b.Open).Returns(100m + i);
            bar.Setup(b => b.High).Returns(102m + i);
            bar.Setup(b => b.Low).Returns(99m + i);
            bar.Setup(b => b.Close).Returns(101m + i);
            bar.Setup(b => b.Volume).Returns(1000000);
            bars.Add(bar.Object);
        }

        return bars;
    }

    private List<IBar> CreateBarsForAtrTest(int count = 20)
    {
        var bars = new List<IBar>();
        var baseDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var random = new Random(42); // Fixed seed for reproducible tests

        for (int i = 0; i < count; i++)
        {
            var basePrice = 100m + i * 0.5m;
            var volatility = 2m;

            var bar = new Mock<IBar>();
            bar.Setup(b => b.TimeUtc).Returns(baseDate.AddDays(i));
            bar.Setup(b => b.Open).Returns(basePrice);
            bar.Setup(b => b.High).Returns(basePrice + (decimal)(random.NextDouble() * (double)volatility));
            bar.Setup(b => b.Low).Returns(basePrice - (decimal)(random.NextDouble() * (double)volatility));
            bar.Setup(b => b.Close).Returns(basePrice + (decimal)((random.NextDouble() - 0.5) * (double)volatility));
            bar.Setup(b => b.Volume).Returns(1000000);
            bars.Add(bar.Object);
        }

        return bars;
    }

    private List<IBar> CreateBarsForSmaTest(int count)
    {
        var bars = new List<IBar>();
        var baseDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        for (int i = 0; i < count; i++)
        {
            var price = 100m + (decimal)Math.Sin(i * 0.1) * 2; // Oscillating around 100
            
            var bar = new Mock<IBar>();
            bar.Setup(b => b.TimeUtc).Returns(baseDate.AddDays(i));
            bar.Setup(b => b.Open).Returns(price);
            bar.Setup(b => b.High).Returns(price + 1);
            bar.Setup(b => b.Low).Returns(price - 1);
            bar.Setup(b => b.Close).Returns(price);
            bar.Setup(b => b.Volume).Returns(1000000);
            bars.Add(bar.Object);
        }

        return bars;
    }

    private List<IBar> CreateBarsForReturnTest()
    {
        var bars = new List<IBar>();
        var baseDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        // Create 10 bars with prices going from 100 to 110 (10% increase)
        for (int i = 0; i < 10; i++)
        {
            var price = 100m + i; // Linear increase from 100 to 109
            
            var bar = new Mock<IBar>();
            bar.Setup(b => b.TimeUtc).Returns(baseDate.AddDays(i));
            bar.Setup(b => b.Open).Returns(price);
            bar.Setup(b => b.High).Returns(price + 0.5m);
            bar.Setup(b => b.Low).Returns(price - 0.5m);
            bar.Setup(b => b.Close).Returns(price);
            bar.Setup(b => b.Volume).Returns(1000000);
            bars.Add(bar.Object);
        }

        // Last bar with close price of 110 for exactly 10% return from start (100)
        var lastBar = new Mock<IBar>();
        lastBar.Setup(b => b.TimeUtc).Returns(baseDate.AddDays(9));
        lastBar.Setup(b => b.Open).Returns(109m);
        lastBar.Setup(b => b.High).Returns(110.5m);
        lastBar.Setup(b => b.Low).Returns(108.5m);
        lastBar.Setup(b => b.Close).Returns(110m);
        lastBar.Setup(b => b.Volume).Returns(1000000);
        bars[9] = lastBar.Object;

        return bars;
    }
}
