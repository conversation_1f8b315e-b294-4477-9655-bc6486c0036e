using AlpacaMomentumBot.Console.Services;
using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Console.Examples;

/// <summary>
/// Demonstrates how to use the new SignalGenerator.RunAsync() method
/// </summary>
public class RunAsyncDemo
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly ILogger<RunAsyncDemo> _logger;

    public RunAsyncDemo(ISignalGenerator signalGenerator, ILogger<RunAsyncDemo> logger)
    {
        _signalGenerator = signalGenerator;
        _logger = logger;
    }

    /// <summary>
    /// Demonstrates the RunAsync method with different configurations
    /// </summary>
    public async Task RunDemoAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting RunAsync demonstration");

        try
        {
            // Example 1: Get top 5 momentum signals
            _logger.LogInformation("=== Getting Top 5 Momentum Signals ===");
            var top5Signals = await _signalGenerator.RunAsync(5, cancellationToken);
            
            foreach (var signal in top5Signals)
            {
                _logger.LogInformation(
                    "Signal: {Symbol} | Price: {Price:C} | ATR: {Atr:F2} | Confidence: {Confidence:P} | {Reason}",
                    signal.Symbol, signal.Price, signal.Atr, signal.Confidence, signal.Reason);
            }

            // Example 2: Get top 10 momentum signals (default)
            _logger.LogInformation("\n=== Getting Top 10 Momentum Signals (Default) ===");
            var top10Signals = await _signalGenerator.RunAsync(cancellationToken: cancellationToken);
            
            _logger.LogInformation("Found {Count} signals meeting momentum criteria:", top10Signals.Count);
            
            for (int i = 0; i < top10Signals.Count; i++)
            {
                var signal = top10Signals[i];
                _logger.LogInformation(
                    "#{Rank}: {Symbol} - Price: {Price:C}, ATR: {Atr:F2}, Confidence: {Confidence:P}",
                    i + 1, signal.Symbol, signal.Price, signal.Atr, signal.Confidence);
            }

            // Example 3: Show filtering criteria in action
            _logger.LogInformation("\n=== Filtering Criteria Applied ===");
            _logger.LogInformation("✓ Close > SMA50 (bullish short-term trend)");
            _logger.LogInformation("✓ Close > SMA200 (bullish long-term trend)");
            _logger.LogInformation("✓ ATR14/Close < 0.03 (low volatility filter)");
            _logger.LogInformation("✓ Ranked by 6-month return (momentum ranking)");

            if (top10Signals.Any())
            {
                var bestSignal = top10Signals.First();
                _logger.LogInformation(
                    "\nBest momentum candidate: {Symbol} with {Confidence:P} confidence",
                    bestSignal.Symbol, bestSignal.Confidence);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in RunAsync demonstration");
        }

        _logger.LogInformation("RunAsync demonstration completed");
    }
}

/// <summary>
/// Example usage in a console application
/// </summary>
public static class RunAsyncUsageExample
{
    public static async Task ExampleUsage()
    {
        // This shows how you might use RunAsync in a real application
        
        /*
        // Setup DI container (example)
        var services = new ServiceCollection();
        services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
        services.AddSingleton<IUniverseProvider, UniverseProvider>();
        services.AddScoped<ISignalGenerator, SignalGenerator>();
        services.AddLogging(builder => builder.AddConsole());
        
        var serviceProvider = services.BuildServiceProvider();
        var signalGenerator = serviceProvider.GetRequiredService<ISignalGenerator>();
        
        // Get top momentum signals
        var signals = await signalGenerator.RunAsync(topN: 10);
        
        // Process signals for trading
        foreach (var signal in signals)
        {
            Console.WriteLine($"Consider buying {signal.Symbol} at {signal.Price:C}");
            Console.WriteLine($"  ATR: {signal.Atr:F2} (volatility indicator)");
            Console.WriteLine($"  Confidence: {signal.Confidence:P}");
            Console.WriteLine($"  Reason: {signal.Reason}");
            Console.WriteLine();
        }
        */
    }
}
