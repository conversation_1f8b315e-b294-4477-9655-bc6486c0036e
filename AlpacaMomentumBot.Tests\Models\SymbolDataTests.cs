using AlpacaMomentumBot.Console.Models;
using FluentAssertions;

namespace AlpacaMomentumBot.Tests.Models;

public class SymbolDataTests
{
    [Fact]
    public void MeetsFilterCriteria_WithValidBullishData_ShouldReturnTrue()
    {
        // Arrange
        var symbolData = new SymbolData(
            Symbol: "AAPL",
            Close: 150m,
            Sma50: 140.0,
            Sma200: 130.0,
            Atr14: 3.0,
            SixMonthReturn: 0.15
        );

        // Act & Assert
        symbolData.MeetsFilterCriteria.Should().BeTrue();
        symbolData.VolatilityRatio.Should().BeApproximately(0.02, 0.001); // 3.0 / 150 = 0.02
    }

    [Fact]
    public void MeetsFilterCriteria_WithPriceBelowSma50_ShouldReturnFalse()
    {
        // Arrange
        var symbolData = new SymbolData(
            Symbol: "AAPL",
            Close: 130m, // Below SMA50
            Sma50: 140.0,
            Sma200: 120.0,
            Atr14: 3.0,
            SixMonthReturn: 0.15
        );

        // Act & Assert
        symbolData.MeetsFilterCriteria.Should().BeFalse();
    }

    [Fact]
    public void MeetsFilterCriteria_WithPriceBelowSma200_ShouldReturnFalse()
    {
        // Arrange
        var symbolData = new SymbolData(
            Symbol: "AAPL",
            Close: 125m, // Below SMA200
            Sma50: 120.0,
            Sma200: 130.0,
            Atr14: 3.0,
            SixMonthReturn: 0.15
        );

        // Act & Assert
        symbolData.MeetsFilterCriteria.Should().BeFalse();
    }

    [Fact]
    public void MeetsFilterCriteria_WithHighVolatility_ShouldReturnFalse()
    {
        // Arrange
        var symbolData = new SymbolData(
            Symbol: "AAPL",
            Close: 150m,
            Sma50: 140.0,
            Sma200: 130.0,
            Atr14: 5.0, // High ATR: 5.0 / 150 = 0.033 > 0.03
            SixMonthReturn: 0.15
        );

        // Act & Assert
        symbolData.MeetsFilterCriteria.Should().BeFalse();
        symbolData.VolatilityRatio.Should().BeApproximately(0.0333, 0.001);
    }

    [Fact]
    public void MeetsFilterCriteria_WithExactVolatilityThreshold_ShouldReturnFalse()
    {
        // Arrange
        var symbolData = new SymbolData(
            Symbol: "AAPL",
            Close: 100m,
            Sma50: 95.0,
            Sma200: 90.0,
            Atr14: 3.0, // Exactly 0.03 volatility ratio
            SixMonthReturn: 0.15
        );

        // Act & Assert
        symbolData.MeetsFilterCriteria.Should().BeFalse(); // Should be < 0.03, not <= 0.03
        symbolData.VolatilityRatio.Should().Be(0.03);
    }

    [Fact]
    public void MeetsFilterCriteria_WithJustBelowVolatilityThreshold_ShouldReturnTrue()
    {
        // Arrange
        var symbolData = new SymbolData(
            Symbol: "AAPL",
            Close: 100m,
            Sma50: 95.0,
            Sma200: 90.0,
            Atr14: 2.99, // Just below 0.03 volatility ratio
            SixMonthReturn: 0.15
        );

        // Act & Assert
        symbolData.MeetsFilterCriteria.Should().BeTrue();
        symbolData.VolatilityRatio.Should().BeApproximately(0.0299, 0.001);
    }

    [Fact]
    public void VolatilityRatio_ShouldCalculateCorrectly()
    {
        // Arrange
        var symbolData = new SymbolData(
            Symbol: "AAPL",
            Close: 200m,
            Sma50: 190.0,
            Sma200: 180.0,
            Atr14: 4.0,
            SixMonthReturn: 0.10
        );

        // Act & Assert
        symbolData.VolatilityRatio.Should().Be(0.02); // 4.0 / 200 = 0.02
    }

    [Theory]
    [InlineData(100, 95, 90, 2.5, true)]  // All criteria met
    [InlineData(90, 95, 90, 2.5, false)]  // Price < SMA50
    [InlineData(100, 95, 105, 2.5, false)] // Price < SMA200
    [InlineData(100, 95, 90, 3.5, false)]  // High volatility (3.5/100 = 0.035 > 0.03)
    [InlineData(85, 95, 90, 2.5, false)]   // Price below both SMAs
    public void MeetsFilterCriteria_WithVariousInputs_ShouldReturnExpectedResult(
        decimal close, double sma50, double sma200, double atr14, bool expected)
    {
        // Arrange
        var symbolData = new SymbolData("TEST", close, sma50, sma200, atr14, 0.10);

        // Act & Assert
        symbolData.MeetsFilterCriteria.Should().Be(expected);
    }
}
