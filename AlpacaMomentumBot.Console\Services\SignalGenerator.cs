using Microsoft.Extensions.Logging;
using Skender.Stock.Indicators;
using AlpacaMomentumBot.Console.Extensions;
using AlpacaMomentumBot.Console.Models;

namespace AlpacaMomentumBot.Console.Services;

public class SignalGenerator : ISignalGenerator
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILogger<SignalGenerator> _logger;

    public SignalGenerator(IAlpacaClientFactory clientFactory, IUniverseProvider universeProvider, ILogger<SignalGenerator> logger)
    {
        _clientFactory = clientFactory;
        _universeProvider = universeProvider;
        _logger = logger;
    }

    public async Task<TradingSignal> GenerateSignalAsync(string symbol, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating signal for {Symbol}", symbol);

            using var dataClient = _clientFactory.CreateDataClient();
            
            // Get historical data for SMA calculation
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddDays(-60); // Get 60 days of data for SMA calculation

            var request = new Alpaca.Markets.HistoricalBarsRequest(symbol, startTime, endTime, Alpaca.Markets.BarTimeFrame.Day);

            var barsPage = await dataClient.ListHistoricalBarsAsync(request, cancellationToken);
            var bars = barsPage.Items.ToList();

            if (bars.Count < 50)
            {
                _logger.LogWarning("Insufficient data for {Symbol}. Got {Count} bars, need at least 50", symbol, bars.Count);
                return new TradingSignal(symbol, SignalType.None, 0, DateTime.UtcNow, "Insufficient historical data");
            }

            // Convert to Quote format for Skender indicators
            var quotes = bars.Select(bar => new Quote
            {
                Date = bar.TimeUtc,
                Open = bar.Open,
                High = bar.High,
                Low = bar.Low,
                Close = bar.Close,
                Volume = bar.Volume
            }).OrderBy(q => q.Date).ToList();

            // Calculate SMAs
            var sma20 = quotes.GetSma(20).ToList();
            var sma50 = quotes.GetSma(50).ToList();

            // Get the latest values
            var latestSma20 = sma20.LastOrDefault()?.Sma;
            var latestSma50 = sma50.LastOrDefault()?.Sma;
            var currentPrice = quotes.Last().Close;

            // NOTE: The above code could be simplified using the new IndicatorExtensions:
            // var quotes = bars.ToQuotes();
            // var latestSma50 = bars.GetSma50();
            // var latestSma200 = bars.GetSma200(); // For longer-term trend analysis
            // var atr14 = bars.GetAtr14(); // For volatility-based position sizing
            // var totalReturn30 = bars.GetTotalReturn(30); // For momentum analysis

            if (!latestSma20.HasValue || !latestSma50.HasValue)
            {
                _logger.LogWarning("Could not calculate SMAs for {Symbol}", symbol);
                return new TradingSignal(symbol, SignalType.None, 0, DateTime.UtcNow, "Could not calculate SMAs");
            }

            // Simple momentum strategy: Buy when price > SMA20 > SMA50, Sell when price < SMA20 < SMA50
            var signal = GenerateMomentumSignal(symbol, currentPrice, (decimal)latestSma20.Value, (decimal)latestSma50.Value);

            _logger.LogInformation("Generated {SignalType} signal for {Symbol} (Price: {Price:C}, SMA20: {Sma20:C}, SMA50: {Sma50:C})",
                signal.Type, symbol, currentPrice, (decimal)latestSma20.Value, (decimal)latestSma50.Value);

            return signal;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating signal for {Symbol}", symbol);
            return new TradingSignal(symbol, SignalType.None, 0, DateTime.UtcNow, $"Error: {ex.Message}");
        }
    }

    public async Task<List<TradingSignal>> RunAsync(int topN = 10, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting signal generation run for top {TopN} symbols", topN);

            // 1. Get universe of symbols
            var universe = await _universeProvider.GetUniverseAsync(cancellationToken);
            _logger.LogInformation("Analyzing {Count} symbols from universe", universe.Count);

            // 2. Analyze each symbol in parallel (with concurrency limit)
            var symbolDataList = new List<SymbolData>();
            var semaphore = new SemaphoreSlim(10, 10); // Limit concurrent API calls

            var tasks = universe.Select(async symbol =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    return await AnalyzeSymbolAsync(symbol, cancellationToken);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            var results = await Task.WhenAll(tasks);
            symbolDataList.AddRange(results.Where(r => r != null)!);

            _logger.LogInformation("Successfully analyzed {Count} symbols", symbolDataList.Count);

            // 3. Filter symbols based on criteria
            var filteredSymbols = symbolDataList
                .Where(s => s.MeetsFilterCriteria)
                .ToList();

            _logger.LogInformation("Filtered to {Count} symbols meeting criteria", filteredSymbols.Count);

            // 4. Order by six-month return descending and take top N
            var topSymbols = filteredSymbols
                .OrderByDescending(s => s.SixMonthReturn)
                .Take(topN)
                .ToList();

            _logger.LogInformation("Selected top {Count} symbols by six-month return", topSymbols.Count);

            // 5. Convert to TradingSignal objects
            var signals = topSymbols.Select(s => new TradingSignal(
                Symbol: s.Symbol,
                Type: SignalType.Buy, // All filtered symbols are buy candidates
                Confidence: CalculateConfidence(s),
                GeneratedAt: DateTime.UtcNow,
                Reason: $"Momentum signal: 6M return {s.SixMonthReturn:P2}, volatility {s.VolatilityRatio:P2}",
                Atr: s.Atr14,
                Price: s.Close
            )).ToList();

            _logger.LogInformation("Generated {Count} trading signals", signals.Count);
            return signals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in RunAsync");
            return new List<TradingSignal>();
        }
    }

    private async Task<SymbolData?> AnalyzeSymbolAsync(string symbol, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Analyzing symbol {Symbol}", symbol);

            using var dataClient = _clientFactory.CreateDataClient();

            // Get 250 daily bars (approximately 1 year of trading data)
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddDays(-365); // Get extra days to ensure we have 250 bars

            var request = new Alpaca.Markets.HistoricalBarsRequest(symbol, startTime, endTime, Alpaca.Markets.BarTimeFrame.Day);
            var barsPage = await dataClient.ListHistoricalBarsAsync(request, cancellationToken);
            var bars = barsPage.Items.ToList();

            // Check if we have sufficient data
            if (bars.Count < 250)
            {
                _logger.LogDebug("Insufficient data for {Symbol}. Got {Count} bars, need 250", symbol, bars.Count);
                return null;
            }

            // Take the most recent 250 bars
            var recentBars = bars.OrderBy(b => b.TimeUtc).TakeLast(250).ToList();

            // Calculate indicators using our extensions
            var close = recentBars.Last().Close;
            var sma50 = recentBars.GetSma50();
            var sma200 = recentBars.GetSma200();
            var atr14 = recentBars.GetAtr14();
            var sixMonthReturn = recentBars.GetTotalReturn(126); // ~6 months of trading days

            return new SymbolData(symbol, close, sma50, sma200, atr14, sixMonthReturn);
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Error analyzing symbol {Symbol}", symbol);
            return null;
        }
    }

    private decimal CalculateConfidence(SymbolData symbolData)
    {
        // Calculate confidence based on multiple factors
        var momentumScore = (decimal)symbolData.SixMonthReturn; // Higher return = higher confidence
        var trendScore = Math.Min(1.0m, (symbolData.Close - (decimal)symbolData.Sma50) / (decimal)symbolData.Sma50 * 10);
        var volatilityScore = 1.0m - (decimal)symbolData.VolatilityRatio * 33.33m; // Lower volatility = higher confidence

        var confidence = (momentumScore * 0.5m + trendScore * 0.3m + volatilityScore * 0.2m);
        return Math.Max(0.1m, Math.Min(1.0m, confidence)); // Clamp between 0.1 and 1.0
    }

    private TradingSignal GenerateMomentumSignal(string symbol, decimal currentPrice, decimal sma20, decimal sma50)
    {
        var now = DateTime.UtcNow;

        // Buy signal: Price above SMA20 and SMA20 above SMA50 (uptrend)
        if (currentPrice > sma20 && sma20 > sma50)
        {
            var confidence = Math.Min(1.0m, (currentPrice - sma20) / sma20 * 10); // Simple confidence calculation
            return new TradingSignal(symbol, SignalType.Buy, confidence, now,
                $"Bullish momentum: Price ({currentPrice:C}) > SMA20 ({sma20:C}) > SMA50 ({sma50:C})");
        }

        // Sell signal: Price below SMA20 and SMA20 below SMA50 (downtrend)
        if (currentPrice < sma20 && sma20 < sma50)
        {
            var confidence = Math.Min(1.0m, (sma20 - currentPrice) / sma20 * 10); // Simple confidence calculation
            return new TradingSignal(symbol, SignalType.Sell, confidence, now,
                $"Bearish momentum: Price ({currentPrice:C}) < SMA20 ({sma20:C}) < SMA50 ({sma50:C})");
        }

        // No clear signal
        return new TradingSignal(symbol, SignalType.None, 0, now,
            $"No clear momentum: Price ({currentPrice:C}), SMA20 ({sma20:C}), SMA50 ({sma50:C})");
    }
}
