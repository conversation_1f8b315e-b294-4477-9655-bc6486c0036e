namespace AlpacaMomentumBot.Console.Services;

/// <summary>
/// Provides the universe of symbols to analyze
/// </summary>
public interface IUniverseProvider
{
    /// <summary>
    /// Gets the universe of symbols including SPY and top-500 tickers by dollar volume
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of symbols to analyze</returns>
    Task<List<string>> GetUniverseAsync(CancellationToken cancellationToken = default);
}
