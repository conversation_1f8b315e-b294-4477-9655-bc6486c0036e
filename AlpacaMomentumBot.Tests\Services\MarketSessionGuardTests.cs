[Fact]
public async Task Guard_When_Weekend_ReturnsFalse()
{
    var alpaca = new Mock<IAlpacaTradingClient>();
    alpaca.Setup(x => x.ListCalendarAsync(It.IsAny<DateOnly>(),
                                          It.IsAny<DateOnly>(),
                                          It.IsAny<CancellationToken>()))
          .ReturnsAsync(Array.Empty<ICalendar>());
    var guard = new MarketSessionGuard(new FakeFactory(alpaca.Object));

    var ok = await guard.CanTradeNowAsync();

    ok.Should().BeFalse();
    guard.Reason.Should().Contain("closed today");
}
