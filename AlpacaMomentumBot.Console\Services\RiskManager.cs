using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Console.Services;

public class RiskManager : IRiskManager
{
    private readonly ILogger<RiskManager> _logger;
    private readonly IAlpacaClientFactory _clientFactory;
    private const decimal MaxPositionSizePercent = 0.05m; // 5% of portfolio per position
    private const decimal StopLossPercent = 0.02m; // 2% stop loss
    private const decimal TakeProfitPercent = 0.06m; // 6% take profit (3:1 risk/reward)
    private const decimal RiskPercentPerTrade = 0.01m; // 1% risk per trade (10bps per $100k)
    private const decimal MaxRiskDollars = 1000m; // $1000 cap

    public RiskManager(ILogger<RiskManager> logger, IAlpacaClientFactory clientFactory)
    {
        _logger = logger;
        _clientFactory = clientFactory;
    }

    public async Task<RiskAssessment> AssessRiskAsync(TradingSignal signal, decimal portfolioValue, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Assessing risk for {Symbol} signal: {SignalType}", signal.Symbol, signal.Type);

            // Basic risk checks
            if (signal.Type == SignalType.None)
            {
                return new RiskAssessment(false, 0, 0, 0, "No trading signal");
            }

            if (signal.Confidence < 0.3m)
            {
                return new RiskAssessment(false, 0, 0, 0, $"Signal confidence too low: {signal.Confidence:P}");
            }

            if (portfolioValue <= 0)
            {
                return new RiskAssessment(false, 0, 0, 0, "Invalid portfolio value");
            }

            // Determine price and position sizing
            decimal price;
            decimal maxShares;

            // Use signal price and ATR if available for more accurate position sizing
            if (signal.Price.HasValue && signal.Atr.HasValue && signal.Price.Value > 0 && signal.Atr.Value > 0)
            {
                price = signal.Price.Value;
                maxShares = await CalculateQuantityAsync(price, signal.Atr.Value, cancellationToken);
                _logger.LogInformation("Using ATR-based position sizing for {Symbol}: Price={Price:C}, ATR={Atr:F4}, Quantity={Quantity:F6}",
                    signal.Symbol, price, signal.Atr.Value, maxShares);
            }
            else
            {
                // Fallback to percentage-based position sizing
                var maxPositionValue = portfolioValue * MaxPositionSizePercent;
                price = 100m; // Placeholder - should get real current price
                maxShares = Math.Floor(maxPositionValue / price);
                _logger.LogInformation("Using percentage-based position sizing for {Symbol}: Max Value={MaxValue:C}, Estimated Price={Price:C}, Quantity={Quantity:F6}",
                    signal.Symbol, maxPositionValue, price, maxShares);
            }

            // Calculate stop loss and take profit levels
            decimal stopLossPrice, takeProfitPrice;

            if (signal.Type == SignalType.Buy)
            {
                stopLossPrice = price * (1 - StopLossPercent);
                takeProfitPrice = price * (1 + TakeProfitPercent);
            }
            else // Sell signal
            {
                stopLossPrice = price * (1 + StopLossPercent);
                takeProfitPrice = price * (1 - TakeProfitPercent);
            }

            var assessment = new RiskAssessment(
                IsApproved: true,
                MaxPositionSize: maxShares,
                StopLossPrice: stopLossPrice,
                TakeProfitPrice: takeProfitPrice,
                RiskReason: $"Approved: Max position {maxShares:F6} shares, Risk/Reward 1:3"
            );

            _logger.LogInformation("Risk assessment for {Symbol}: {IsApproved}, Max Size: {MaxSize:F6}, Stop: {Stop:C}, Target: {Target:C}",
                signal.Symbol, assessment.IsApproved, assessment.MaxPositionSize, assessment.StopLossPrice, assessment.TakeProfitPrice);

            return assessment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assessing risk for {Symbol}", signal.Symbol);
            return new RiskAssessment(false, 0, 0, 0, $"Risk assessment error: {ex.Message}");
        }
    }

    public async Task<decimal> CalculateQuantityAsync(decimal price, double atr14, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Calculating position quantity for price: {Price:C}, ATR14: {Atr14:F4}", price, atr14);

            // Validate inputs
            if (price <= 0)
            {
                _logger.LogWarning("Invalid price: {Price}", price);
                return 0m;
            }

            if (atr14 <= 0)
            {
                _logger.LogWarning("Invalid ATR14: {Atr14}", atr14);
                return 0m;
            }

            // Get account information
            using var tradingClient = _clientFactory.CreateTradingClient();
            var account = await tradingClient.GetAccountAsync(cancellationToken);

            if (account.Equity == null || account.Equity <= 0)
            {
                _logger.LogWarning("Invalid account equity: {Equity}", account.Equity);
                return 0m;
            }

            // Calculate risk dollars: min(account.Equity * 0.01m, 1000m)
            var riskDollars = Math.Min(account.Equity.Value * RiskPercentPerTrade, MaxRiskDollars);

            // Calculate quantity: riskDollars / (atr14 * price)
            var atr14Decimal = (decimal)atr14;
            var quantity = riskDollars / (atr14Decimal * price);

            _logger.LogInformation("Risk calculation - Equity: {Equity:C}, Risk Dollars: {RiskDollars:C}, Quantity: {Quantity:F6}",
                account.Equity.Value, riskDollars, quantity);

            // Validate that quantity doesn't exceed the tolerance: qty <= riskDollars / price
            var maxQuantityByRisk = riskDollars / price;
            if (quantity > maxQuantityByRisk)
            {
                _logger.LogWarning("Calculated quantity {Quantity:F6} exceeds risk tolerance {MaxQuantity:F6}, using max quantity",
                    quantity, maxQuantityByRisk);
                quantity = maxQuantityByRisk;
            }

            return quantity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating quantity for price: {Price:C}, ATR14: {Atr14:F4}", price, atr14);
            return 0m;
        }
    }
}
