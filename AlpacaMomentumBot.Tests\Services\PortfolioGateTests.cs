using AlpacaMomentumBot.Console.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace AlpacaMomentumBot.Tests.Services;

public class PortfolioGateTests
{
    private readonly Mock<IAlpacaClientFactory> _mockClientFactory;
    private readonly Mock<IAlpacaDataClient> _mockDataClient;
    private readonly Mock<ILogger<PortfolioGate>> _mockLogger;
    private readonly PortfolioGate _portfolioGate;

    public PortfolioGateTests()
    {
        _mockClientFactory = new Mock<IAlpacaClientFactory>();
        _mockDataClient = new Mock<IAlpacaDataClient>();
        _mockLogger = new Mock<ILogger<PortfolioGate>>();

        _mockClientFactory.Setup(f => f.CreateDataClient()).Returns(_mockDataClient.Object);

        _portfolioGate = new PortfolioGate(_mockClientFactory.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task ShouldTradeAsync_WhenSpyCloseAboveSma200_ShouldReturnTrue()
    {
        // Arrange
        var spyBars = CreateSpyBarsWithTrend(250, 400m, bullish: true); // SPY close > SMA200
        SetupMockDataForSpy(spyBars);

        // Act
        var result = await _portfolioGate.ShouldTradeAsync();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ShouldTradeAsync_WhenSpyCloseBelowSma200_ShouldReturnFalse()
    {
        // Arrange
        var spyBars = CreateSpyBarsWithTrend(250, 400m, bullish: false); // SPY close < SMA200
        SetupMockDataForSpy(spyBars);

        // Act
        var result = await _portfolioGate.ShouldTradeAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task ShouldTradeAsync_WhenSpyCloseEqualsSma200_ShouldReturnTrue()
    {
        // Arrange
        var spyBars = CreateSpyBarsAtSma200(250, 400m); // SPY close = SMA200
        SetupMockDataForSpy(spyBars);

        // Act
        var result = await _portfolioGate.ShouldTradeAsync();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ShouldTradeAsync_WithInsufficientData_ShouldReturnFalse()
    {
        // Arrange
        var insufficientBars = CreateSpyBarsWithTrend(150, 400m, bullish: true); // Only 150 bars, need 200
        SetupMockDataForSpy(insufficientBars);

        // Act
        var result = await _portfolioGate.ShouldTradeAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task ShouldTradeAsync_WhenDataClientThrowsException_ShouldReturnFalse()
    {
        // Arrange
        _mockDataClient.Setup(c => c.ListHistoricalBarsAsync(
            It.IsAny<HistoricalBarsRequest>(),
            It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("API Error"));

        // Act
        var result = await _portfolioGate.ShouldTradeAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task ShouldTradeAsync_WithCancellationToken_ShouldPassTokenToDataClient()
    {
        // Arrange
        var spyBars = CreateSpyBarsWithTrend(250, 400m, bullish: true);
        SetupMockDataForSpy(spyBars);
        var cancellationToken = new CancellationToken();

        // Act
        await _portfolioGate.ShouldTradeAsync(cancellationToken);

        // Assert
        _mockDataClient.Verify(c => c.ListHistoricalBarsAsync(
            It.IsAny<HistoricalBarsRequest>(),
            cancellationToken), Times.Once);
    }

    private void SetupMockDataForSpy(List<IBar> bars)
    {
        var mockBarsPage = new Mock<IPage<IBar>>();
        mockBarsPage.Setup(p => p.Items).Returns(bars);

        _mockDataClient.Setup(c => c.ListHistoricalBarsAsync(
            It.Is<HistoricalBarsRequest>(r => r.Symbols.Contains("SPY")),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockBarsPage.Object);
    }

    private List<IBar> CreateSpyBarsWithTrend(int count, decimal basePrice, bool bullish)
    {
        var bars = new List<IBar>();
        var baseDate = DateTime.UtcNow.AddDays(-count);

        for (int i = 0; i < count; i++)
        {
            decimal currentPrice;
            
            if (bullish)
            {
                // Create upward trend where recent prices are higher than earlier prices
                // This ensures close > SMA200
                currentPrice = basePrice + (i * 0.5m); // Gradual upward trend
            }
            else
            {
                // Create downward trend where recent prices are lower than earlier prices
                // This ensures close < SMA200
                currentPrice = basePrice - (i * 0.3m); // Gradual downward trend
            }

            var bar = new Mock<IBar>();
            bar.Setup(b => b.TimeUtc).Returns(baseDate.AddDays(i));
            bar.Setup(b => b.Open).Returns(currentPrice);
            bar.Setup(b => b.High).Returns(currentPrice + 1m);
            bar.Setup(b => b.Low).Returns(currentPrice - 1m);
            bar.Setup(b => b.Close).Returns(currentPrice);
            bar.Setup(b => b.Volume).Returns(50000000); // Typical SPY volume
            bars.Add(bar.Object);
        }

        return bars;
    }

    private List<IBar> CreateSpyBarsAtSma200(int count, decimal basePrice)
    {
        var bars = new List<IBar>();
        var baseDate = DateTime.UtcNow.AddDays(-count);

        // Create bars where the close price equals the SMA200
        // We'll create flat prices for most bars, then adjust the last one
        for (int i = 0; i < count; i++)
        {
            var currentPrice = basePrice; // Keep prices flat so SMA200 ≈ basePrice

            var bar = new Mock<IBar>();
            bar.Setup(b => b.TimeUtc).Returns(baseDate.AddDays(i));
            bar.Setup(b => b.Open).Returns(currentPrice);
            bar.Setup(b => b.High).Returns(currentPrice + 0.5m);
            bar.Setup(b => b.Low).Returns(currentPrice - 0.5m);
            bar.Setup(b => b.Close).Returns(currentPrice);
            bar.Setup(b => b.Volume).Returns(50000000);
            bars.Add(bar.Object);
        }

        return bars;
    }
}
