using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Console.Services;

/// <summary>
/// Provides the universe of symbols to analyze
/// </summary>
public class UniverseProvider : IUniverseProvider
{
    private readonly ILogger<UniverseProvider> _logger;

    // For now, we'll use a static list of popular symbols
    // In a production system, this could be fetched from a data provider
    private readonly List<string> _topSymbols = new()
    {
        // Major ETFs
        "SPY", "QQQ", "IWM", "VTI", "VOO", "VEA", "VWO", "AGG", "LQD", "HYG",
        "GLD", "SLV", "USO", "TLT", "EFA", "EEM", "XLF", "XLE", "XLK", "XLV",
        
        // Large Cap Tech
        "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "TSLA", "META", "NVDA", "NFLX", "ADBE",
        "CRM", "ORCL", "INTC", "AMD", "QCOM", "AVGO", "TXN", "CSCO", "IBM", "UBER",
        
        // Large Cap Non-Tech
        "BRK.B", "UNH", "JNJ", "V", "PG", "JPM", "HD", "MA", "DIS", "PYPL",
        "BAC", "XOM", "CVX", "PFE", "KO", "PEP", "WMT", "MRK", "ABT", "TMO",
        "COST", "ABBV", "ACN", "NKE", "MCD", "DHR", "NEE", "LIN", "VZ", "ADBE",
        
        // Mid Cap Growth
        "ROKU", "SQ", "SHOP", "TWLO", "OKTA", "DDOG", "SNOW", "PLTR", "CRWD", "ZM",
        "DOCU", "PTON", "PINS", "SNAP", "SPOT", "ZS", "FSLY", "NET", "ESTC", "MDB",
        
        // Financial Services
        "GS", "MS", "C", "WFC", "USB", "PNC", "TFC", "COF", "AXP", "BLK",
        "SCHW", "CB", "MMC", "AON", "SPGI", "MCO", "ICE", "CME", "NDAQ", "MSCI",
        
        // Healthcare & Biotech
        "AMGN", "GILD", "BIIB", "REGN", "VRTX", "ILMN", "MRNA", "BNTX", "JNJ", "PFE",
        "BMY", "LLY", "AZN", "NVO", "ROCHE", "GSK", "SNY", "TAK", "ABBV", "MRK",
        
        // Consumer & Retail
        "AMZN", "WMT", "TGT", "LOW", "SBUX", "MCD", "CMG", "YUM", "QSR", "DPZ",
        "NKE", "LULU", "DECK", "RH", "ETSY", "W", "CHWY", "CHEWY", "PETS", "WOOF",
        
        // Industrial & Materials
        "CAT", "DE", "BA", "GE", "MMM", "HON", "UPS", "FDX", "LMT", "RTX",
        "NOC", "GD", "EMR", "ETN", "PH", "ROK", "DOV", "ITW", "CMI", "IR",
        
        // Energy & Utilities
        "XOM", "CVX", "COP", "EOG", "SLB", "HAL", "BKR", "OXY", "MPC", "VLO",
        "KMI", "WMB", "EPD", "ET", "MPLX", "NEE", "DUK", "SO", "D", "EXC",
        
        // Real Estate & REITs
        "AMT", "PLD", "CCI", "EQIX", "PSA", "EXR", "AVB", "EQR", "UDR", "CPT",
        "MAA", "ESS", "AIV", "BXP", "VTR", "WELL", "HCP", "PEAK", "MPW", "O",
        
        // Communication Services
        "GOOGL", "META", "NFLX", "DIS", "CMCSA", "VZ", "T", "CHTR", "TMUS", "DISH",
        "TWTR", "SNAP", "PINS", "MTCH", "IAC", "NWSA", "FOXA", "PARA", "WBD", "SIRI"
    };

    public UniverseProvider(ILogger<UniverseProvider> logger)
    {
        _logger = logger;
    }

    public async Task<List<string>> GetUniverseAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting universe of symbols");

            // Ensure SPY is always included
            var universe = new List<string> { "SPY" };
            
            // Add other symbols, removing duplicates
            universe.AddRange(_topSymbols.Where(s => s != "SPY").Distinct());
            
            // Limit to reasonable number for testing (can be increased in production)
            var result = universe.Take(100).ToList();
            
            _logger.LogInformation("Universe contains {Count} symbols", result.Count);
            
            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting universe");
            
            // Fallback to minimal universe
            return new List<string> { "SPY", "QQQ", "AAPL", "MSFT", "GOOGL" };
        }
    }
}
