namespace AlpacaMomentumBot.Console.Models;

/// <summary>
/// Represents technical analysis data for a symbol
/// </summary>
public record SymbolData(
    string Symbol,
    decimal Close,
    double Sma50,
    double Sma200,
    double Atr14,
    double SixMonthReturn)
{
    /// <summary>
    /// Checks if the symbol meets the filtering criteria:
    /// - Close > SMA50
    /// - Close > SMA200  
    /// - ATR14/Close < 0.03 (volatility filter)
    /// </summary>
    public bool MeetsFilterCriteria => 
        Close > (decimal)Sma50 && 
        Close > (decimal)Sma200 && 
        (Atr14 / (double)Close) < 0.03;

    /// <summary>
    /// Calculates the volatility ratio (ATR14/Close)
    /// </summary>
    public double VolatilityRatio => Atr14 / (double)Close;
}
