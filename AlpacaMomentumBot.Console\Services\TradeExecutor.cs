using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Console.Services;

public class TradeExecutor : ITradeExecutor
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly ILogger<TradeExecutor> _logger;

    public TradeExecutor(IAlpacaClientFactory clientFactory, ILogger<TradeExecutor> logger)
    {
        _clientFactory = clientFactory;
        _logger = logger;
    }

    public async Task<TradeResult> ExecuteTradeAsync(TradingSignal signal, RiskAssessment risk, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Executing trade for {Symbol}: {SignalType}, Size: {Size:F6}",
                signal.Symbol, signal.Type, risk.MaxPositionSize);

            using var tradingClient = _clientFactory.CreateTradingClient();

            // Step 1: Cancel open orders for same symbol
            await CancelOpenOrdersAsync(tradingClient, signal.Symbol, cancellationToken);

            // Only process Buy signals for now (as per requirements)
            if (signal.Type != SignalType.Buy)
            {
                _logger.LogInformation("Skipping non-buy signal for {Symbol}: {SignalType}", signal.Symbol, signal.Type);
                return new TradeResult(false, null, null, null, "Only buy signals are processed");
            }

            // Validate required data
            if (!signal.Price.HasValue || !signal.Atr.HasValue)
            {
                _logger.LogWarning("Missing required price or ATR data for {Symbol}. Price: {Price}, ATR: {Atr}",
                    signal.Symbol, signal.Price, signal.Atr);
                return new TradeResult(false, null, null, null, "Missing price or ATR data");
            }

            var lastClose = signal.Price.Value;
            var atr14 = signal.Atr.Value;

            // Step 2: Submit Limit-on-Open Buy with limit = lastClose * 1.002m
            var limitPrice = lastClose * 1.002m;

            var orderRequest = new NewOrderRequest(
                signal.Symbol,
                OrderQuantity.Fractional(risk.MaxPositionSize),
                OrderSide.Buy,
                OrderType.Limit,
                TimeInForce.Opg) // Open (OPG) - Limit-on-Open
            {
                LimitPrice = limitPrice
            };

            var order = await tradingClient.PostOrderAsync(orderRequest, cancellationToken);

            _logger.LogInformation("Limit-on-Open order submitted: {OrderId} for {Symbol}, Quantity: {Quantity:F6}, Limit: {Limit:C}",
                order.OrderId, signal.Symbol, risk.MaxPositionSize, limitPrice);

            // Step 3: Place Good-Til-Canceled stop-loss at entry - 2×ATR
            var stopLossPrice = lastClose - (2 * (decimal)atr14);

            await SubmitStopLossOrderAsync(tradingClient, signal.Symbol, risk.MaxPositionSize, stopLossPrice, cancellationToken);

            // Return result - for Limit-on-Open orders, we don't wait for immediate execution
            var result = new TradeResult(
                IsSuccessful: true,
                OrderId: order.OrderId.ToString(),
                ExecutedPrice: null, // Will be filled at market open
                ExecutedQuantity: null, // Will be filled at market open
                Message: $"Limit-on-Open order placed: {risk.MaxPositionSize:F6} shares at {limitPrice:C}, Stop-loss at {stopLossPrice:C}"
            );

            _logger.LogInformation("Trade setup completed for {Symbol}: {Message}", signal.Symbol, result.Message);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing trade for {Symbol}", signal.Symbol);
            return new TradeResult(false, null, null, null, $"Execution error: {ex.Message}");
        }
    }

    private async Task CancelOpenOrdersAsync(IAlpacaTradingClient tradingClient, string symbol, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Canceling open orders for {Symbol}", symbol);

            // Get all open orders for the symbol
            var openOrders = await tradingClient.ListOrdersAsync(new ListOrdersRequest
            {
                OrderStatusFilter = OrderStatusFilter.Open
            }, cancellationToken);

            // Filter by symbol since we can't set it directly in the request
            var symbolOrders = openOrders.Where(o => o.Symbol == symbol).ToList();

            if (!symbolOrders.Any())
            {
                _logger.LogInformation("No open orders found for {Symbol}", symbol);
                return;
            }

            _logger.LogInformation("Found {Count} open orders for {Symbol}, canceling...", symbolOrders.Count, symbol);

            // Cancel each open order
            var cancelTasks = symbolOrders.Select(async order =>
            {
                try
                {
                    await tradingClient.CancelOrderAsync(order.OrderId, cancellationToken);
                    _logger.LogInformation("Canceled order {OrderId} for {Symbol}", order.OrderId, symbol);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to cancel order {OrderId} for {Symbol}", order.OrderId, symbol);
                }
            });

            await Task.WhenAll(cancelTasks);
            _logger.LogInformation("Completed canceling orders for {Symbol}", symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error canceling open orders for {Symbol}", symbol);
        }
    }

    private async Task SubmitStopLossOrderAsync(IAlpacaTradingClient tradingClient, string symbol, decimal quantity, decimal stopPrice, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Submitting stop-loss order for {Symbol}: {Quantity:F6} shares at {StopPrice:C}",
                symbol, quantity, stopPrice);

            var stopLossOrder = new NewOrderRequest(
                symbol,
                OrderQuantity.Fractional(quantity),
                OrderSide.Sell,
                OrderType.Stop,
                TimeInForce.Gtc) // Good-Til-Canceled
            {
                StopPrice = stopPrice
            };

            var order = await tradingClient.PostOrderAsync(stopLossOrder, cancellationToken);
            _logger.LogInformation("Stop-loss order submitted: {OrderId} for {Symbol} at {StopPrice:C}",
                order.OrderId, symbol, stopPrice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting stop-loss order for {Symbol}", symbol);
            throw; // Re-throw since this is critical for risk management
        }
    }

    private async Task SubmitBracketOrdersAsync(IAlpacaTradingClient tradingClient, TradingSignal signal, RiskAssessment risk, IOrder parentOrder, CancellationToken cancellationToken)
    {
        try
        {
            if (parentOrder.FilledQuantity == 0)
                return;

            var quantity = parentOrder.FilledQuantity;
            var oppositeSide = signal.Type == SignalType.Buy ? OrderSide.Sell : OrderSide.Buy;

            // Submit stop loss order
            var stopLossOrder = new NewOrderRequest(signal.Symbol, OrderQuantity.Fractional(quantity), oppositeSide, OrderType.Stop, TimeInForce.Gtc)
            {
                StopPrice = risk.StopLossPrice
            };

            var stopOrder = await tradingClient.PostOrderAsync(stopLossOrder, cancellationToken);
            _logger.LogInformation("Stop loss order submitted: {OrderId} at {Price:C}", stopOrder.OrderId, risk.StopLossPrice);

            // Submit take profit order
            var takeProfitOrder = new NewOrderRequest(signal.Symbol, OrderQuantity.Fractional(quantity), oppositeSide, OrderType.Limit, TimeInForce.Gtc)
            {
                LimitPrice = risk.TakeProfitPrice
            };

            var profitOrder = await tradingClient.PostOrderAsync(takeProfitOrder, cancellationToken);
            _logger.LogInformation("Take profit order submitted: {OrderId} at {Price:C}", profitOrder.OrderId, risk.TakeProfitPrice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting bracket orders for {Symbol}", signal.Symbol);
        }
    }
}
