2025-06-15 15:16:34.047 -04:00 [INF] Starting Alpaca Momentum Bot
2025-06-15 15:16:34.134 -04:00 [FTL] Application terminated unexpectedly
System.ArgumentException: The supplied DateTime must have the Kind property set to Utc (Parameter 'fromUtc')
   at Cronos.CronExpression.ThrowWrongDateTimeKindException(String paramName) in C:\projects\cronos\src\Cronos\CronExpression.cs:line 667
   at Cronos.CronExpression.GetNextOccurrence(DateTime fromUtc, TimeZoneInfo zone, Boolean inclusive) in C:\projects\cronos\src\Cronos\CronExpression.cs:line 213
   at AlpacaMomentumBot.Console.Services.ScheduleService.GetNextRunTime() in C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower\AlpacaMomentumBot.Console\Services\ScheduleService.cs:line 75
   at AlpacaMomentumBot.Console.Services.ScheduleService..ctor(IServiceProvider serviceProvider, ILogger`1 logger) in C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower\AlpacaMomentumBot.Console\Services\ScheduleService.cs:line 28
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeDirectByRefWithFewArgs(Object obj, Span`1 copyOfArgs, BindingFlags invokeAttr)
   at System.Reflection.MethodBaseInvoker.InvokeWithFewArgs(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitIEnumerable(IEnumerableCallSite enumerableCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost host, CancellationToken token)
   at Program.<Main>$(String[] args) in C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower\AlpacaMomentumBot.Console\Program.cs:line 41
2025-06-15 15:17:04.001 -04:00 [INF] Starting Alpaca Momentum Bot
2025-06-15 15:17:04.103 -04:00 [INF] ScheduleService initialized. Next run: "2025-06-16T20:10:00.0000000+00:00"
2025-06-15 15:17:04.114 -04:00 [INF] ScheduleService started
2025-06-15 15:17:04.115 -04:00 [INF] Next trading cycle scheduled for "2025-06-16T20:10:00.0000000+00:00" (in "1.00:52:55.8849496")
2025-06-15 15:17:04.118 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-15 15:17:04.119 -04:00 [INF] Hosting environment: Production
2025-06-15 15:17:04.119 -04:00 [INF] Content root path: C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower
2025-06-15 15:57:16.164 -04:00 [INF] Starting Alpaca Momentum Bot
2025-06-15 15:57:16.247 -04:00 [INF] ScheduleService initialized. Next run: "2025-06-16T20:10:00.0000000+00:00"
2025-06-15 15:57:16.257 -04:00 [INF] ScheduleService started
2025-06-15 15:57:16.258 -04:00 [INF] Next trading cycle scheduled for "2025-06-16T20:10:00.0000000+00:00" (in "1.00:12:43.7412223")
2025-06-15 15:57:16.262 -04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-15 15:57:16.262 -04:00 [INF] Hosting environment: Production
2025-06-15 15:57:16.263 -04:00 [INF] Content root path: C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower
