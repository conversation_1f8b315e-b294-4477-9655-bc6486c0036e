namespace AlpacaMomentumBot.Console.Services;

public interface ISignalGenerator
{
    Task<TradingSignal> GenerateSignalAsync(string symbol, CancellationToken cancellationToken = default);
    Task<List<TradingSignal>> RunAsync(int topN = 10, CancellationToken cancellationToken = default);
}

public record TradingSignal(
    string Symbol,
    SignalType Type,
    decimal Confidence,
    DateTime GeneratedAt,
    string Reason,
    double? Atr = null,
    decimal? Price = null);

public enum SignalType
{
    None,
    Buy,
    Sell
}
