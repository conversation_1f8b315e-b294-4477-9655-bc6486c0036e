using AlpacaMomentumBot.Console.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace AlpacaMomentumBot.Tests.Services;

public class TradeExecutorTests
{
    private readonly Mock<IAlpacaClientFactory> _clientFactoryMock;
    private readonly Mock<IAlpacaTradingClient> _tradingClientMock;
    private readonly Mock<ILogger<TradeExecutor>> _loggerMock;
    private readonly Mock<IOrder> _orderMock;
    private readonly TradeExecutor _tradeExecutor;

    public TradeExecutorTests()
    {
        _clientFactoryMock = new Mock<IAlpacaClientFactory>();
        _tradingClientMock = new Mock<IAlpacaTradingClient>();
        _loggerMock = new Mock<ILogger<TradeExecutor>>();
        _orderMock = new Mock<IOrder>();
        
        _clientFactoryMock.Setup(x => x.CreateTradingClient()).Returns(_tradingClientMock.Object);
        
        _tradeExecutor = new TradeExecutor(_clientFactoryMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task ExecuteTradeAsync_WithValidBuySignal_ShouldSubmitLimitOnOpenAndStopLoss()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.8m, DateTime.UtcNow, "Strong buy signal", 2.5, 150m);
        var risk = new RiskAssessment(true, 10.5m, 147m, 159m, "Approved");
        
        _orderMock.Setup(x => x.OrderId).Returns(Guid.NewGuid());
        _tradingClientMock.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IOrder>());
        _tradingClientMock.Setup(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_orderMock.Object);

        // Act
        var result = await _tradeExecutor.ExecuteTradeAsync(signal, risk, CancellationToken.None);

        // Assert
        result.IsSuccessful.Should().BeTrue();
        result.OrderId.Should().NotBeNull();
        result.Message.Should().Contain("Limit-on-Open order placed");
        result.Message.Should().Contain("10.500000 shares");
        result.Message.Should().Contain("$150.30"); // 150 * 1.002
        result.Message.Should().Contain("$145.00"); // 150 - (2 * 2.5)

        // Verify order cancellation was called
        _tradingClientMock.Verify(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), It.IsAny<CancellationToken>()), Times.Once);
        
        // Verify two orders were submitted (limit-on-open + stop-loss)
        _tradingClientMock.Verify(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
    }

    [Fact]
    public async Task ExecuteTradeAsync_WithSellSignal_ShouldSkipExecution()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Sell, 0.8m, DateTime.UtcNow, "Sell signal", 2.5, 150m);
        var risk = new RiskAssessment(true, 10.5m, 147m, 159m, "Approved");

        // Act
        var result = await _tradeExecutor.ExecuteTradeAsync(signal, risk, CancellationToken.None);

        // Assert
        result.IsSuccessful.Should().BeFalse();
        result.Message.Should().Be("Only buy signals are processed");
        
        // Verify no orders were submitted
        _tradingClientMock.Verify(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteTradeAsync_WithMissingPrice_ShouldReturnError()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.8m, DateTime.UtcNow, "Buy signal", 2.5, null); // No price
        var risk = new RiskAssessment(true, 10.5m, 147m, 159m, "Approved");

        // Act
        var result = await _tradeExecutor.ExecuteTradeAsync(signal, risk, CancellationToken.None);

        // Assert
        result.IsSuccessful.Should().BeFalse();
        result.Message.Should().Be("Missing price or ATR data");
        
        // Verify no orders were submitted
        _tradingClientMock.Verify(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteTradeAsync_WithMissingAtr_ShouldReturnError()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.8m, DateTime.UtcNow, "Buy signal", null, 150m); // No ATR
        var risk = new RiskAssessment(true, 10.5m, 147m, 159m, "Approved");

        // Act
        var result = await _tradeExecutor.ExecuteTradeAsync(signal, risk, CancellationToken.None);

        // Assert
        result.IsSuccessful.Should().BeFalse();
        result.Message.Should().Be("Missing price or ATR data");
        
        // Verify no orders were submitted
        _tradingClientMock.Verify(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteTradeAsync_ShouldCancelExistingOrders()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.8m, DateTime.UtcNow, "Buy signal", 2.5, 150m);
        var risk = new RiskAssessment(true, 10.5m, 147m, 159m, "Approved");
        
        var existingOrder = new Mock<IOrder>();
        existingOrder.Setup(x => x.Symbol).Returns("AAPL");
        existingOrder.Setup(x => x.OrderId).Returns(Guid.NewGuid());
        
        _orderMock.Setup(x => x.OrderId).Returns(Guid.NewGuid());
        _tradingClientMock.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IOrder> { existingOrder.Object });
        _tradingClientMock.Setup(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_orderMock.Object);

        // Act
        var result = await _tradeExecutor.ExecuteTradeAsync(signal, risk, CancellationToken.None);

        // Assert
        result.IsSuccessful.Should().BeTrue();
        
        // Verify existing order was canceled
        _tradingClientMock.Verify(x => x.CancelOrderAsync(existingOrder.Object.OrderId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ExecuteTradeAsync_ShouldCalculateCorrectLimitPrice()
    {
        // Arrange
        var lastClose = 100m;
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.8m, DateTime.UtcNow, "Buy signal", 2.0, lastClose);
        var risk = new RiskAssessment(true, 5m, 96m, 106m, "Approved");
        
        _orderMock.Setup(x => x.OrderId).Returns(Guid.NewGuid());
        _tradingClientMock.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IOrder>());
        _tradingClientMock.Setup(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_orderMock.Object);

        // Act
        var result = await _tradeExecutor.ExecuteTradeAsync(signal, risk, CancellationToken.None);

        // Assert
        result.IsSuccessful.Should().BeTrue();
        
        // Verify limit price calculation: 100 * 1.002 = 100.20
        var expectedLimitPrice = lastClose * 1.002m;
        result.Message.Should().Contain($"${expectedLimitPrice:F2}");
        
        // Verify stop-loss price calculation: 100 - (2 * 2.0) = 96.00
        var expectedStopPrice = lastClose - (2 * 2.0m);
        result.Message.Should().Contain($"${expectedStopPrice:F2}");
    }

    [Fact]
    public async Task ExecuteTradeAsync_WithOrderSubmissionFailure_ShouldReturnError()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.8m, DateTime.UtcNow, "Buy signal", 2.0, 100m);
        var risk = new RiskAssessment(true, 5m, 96m, 106m, "Approved");

        _tradingClientMock.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IOrder>());
        _tradingClientMock.Setup(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Order submission failed"));

        // Act
        var result = await _tradeExecutor.ExecuteTradeAsync(signal, risk, CancellationToken.None);

        // Assert
        result.IsSuccessful.Should().BeFalse();
        result.Message.Should().Contain("Execution error");
        result.Message.Should().Contain("Order submission failed");
    }

    [Fact]
    public async Task ExecuteTradeAsync_WithCancellationFailure_ShouldContinueWithOrderSubmission()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.8m, DateTime.UtcNow, "Buy signal", 2.0, 100m);
        var risk = new RiskAssessment(true, 5m, 96m, 106m, "Approved");

        var existingOrder = new Mock<IOrder>();
        existingOrder.Setup(x => x.Symbol).Returns("AAPL");
        existingOrder.Setup(x => x.OrderId).Returns(Guid.NewGuid());

        _orderMock.Setup(x => x.OrderId).Returns(Guid.NewGuid());
        _tradingClientMock.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IOrder> { existingOrder.Object });
        _tradingClientMock.Setup(x => x.CancelOrderAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Cancel failed"));
        _tradingClientMock.Setup(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_orderMock.Object);

        // Act
        var result = await _tradeExecutor.ExecuteTradeAsync(signal, risk, CancellationToken.None);

        // Assert - Should still succeed despite cancellation failure
        result.IsSuccessful.Should().BeTrue();
        result.Message.Should().Contain("Limit-on-Open order placed");
    }

    [Fact]
    public async Task ExecuteTradeAsync_WithZeroQuantity_ShouldStillSubmitOrders()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.8m, DateTime.UtcNow, "Buy signal", 2.0, 100m);
        var risk = new RiskAssessment(true, 0m, 96m, 106m, "Approved"); // Zero quantity

        _orderMock.Setup(x => x.OrderId).Returns(Guid.NewGuid());
        _tradingClientMock.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IOrder>());
        _tradingClientMock.Setup(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_orderMock.Object);

        // Act
        var result = await _tradeExecutor.ExecuteTradeAsync(signal, risk, CancellationToken.None);

        // Assert
        result.IsSuccessful.Should().BeTrue();
        result.Message.Should().Contain("0.000000 shares");

        // Should still submit both orders even with zero quantity
        _tradingClientMock.Verify(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
    }
}
