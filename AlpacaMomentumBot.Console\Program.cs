﻿using AlpacaMomentumBot.Console.Services;
using DotNetEnv;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;

// ---------------------------------------------------------------------------
//  Bootstrap: load env-vars & configure Serilog
// ---------------------------------------------------------------------------
Env.Load();

Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/alpaca-momentum-bot-.log",
                  rollingInterval: RollingInterval.Day,
                  retainedFileCountLimit: 30)
    .CreateLogger();

try
{
    Log.Information("Starting Alpaca Momentum Bot (manual run)");

    // -----------------------------------------------------------------------
    //  DI container
    // -----------------------------------------------------------------------
    using IHost host = Host.CreateDefaultBuilder(args)
        .UseSerilog()
        .ConfigureServices(services =>
        {
            services.AddSingleton<ITimeProvider, SystemTimeProvider>();
            services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
            services.AddSingleton<IUniverseProvider, UniverseProvider>();
            services.AddSingleton<IMarketSessionGuard, MarketSessionGuard>();   // NEW

            services.AddScoped<ISignalGenerator, SignalGenerator>();
            services.AddScoped<IRiskManager, RiskManager>();
            services.AddScoped<IPortfolioGate, PortfolioGate>();
            services.AddScoped<ITradeExecutor, TradeExecutor>();
            services.AddScoped<ITradingService, TradingService>();
        })
        .Build();

    // -----------------------------------------------------------------------
    //  One-shot trading cycle
    // -----------------------------------------------------------------------
    using var scope  = host.Services.CreateScope();
    var guard        = scope.ServiceProvider.GetRequiredService<IMarketSessionGuard>();

    if (!await guard.CanTradeNowAsync())
    {
        Log.Information("Exiting – {Reason}", guard.Reason);
        return;                                     // graceful no-trade exit
    }

    var trader = scope.ServiceProvider.GetRequiredService<ITradingService>();
    await trader.ExecuteCycleAsync(ct);

    Log.Information("Trading cycle completed successfully");
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
