using AlpacaMomentumBot.Console.Extensions;
using AlpacaMomentumBot.Console.Models;
using AlpacaMomentumBot.Console.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace AlpacaMomentumBot.Tests.Services;

public class SignalGeneratorRunAsyncTests
{
    private readonly Mock<IAlpacaClientFactory> _mockClientFactory;
    private readonly Mock<IUniverseProvider> _mockUniverseProvider;
    private readonly Mock<IAlpacaDataClient> _mockDataClient;
    private readonly Mock<ILogger<SignalGenerator>> _mockLogger;
    private readonly SignalGenerator _signalGenerator;

    public SignalGeneratorRunAsyncTests()
    {
        _mockClientFactory = new Mock<IAlpacaClientFactory>();
        _mockUniverseProvider = new Mock<IUniverseProvider>();
        _mockDataClient = new Mock<IAlpacaDataClient>();
        _mockLogger = new Mock<ILogger<SignalGenerator>>();

        _mockClientFactory.Setup(f => f.CreateDataClient()).Returns(_mockDataClient.Object);

        _signalGenerator = new SignalGenerator(
            _mockClientFactory.Object,
            _mockUniverseProvider.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task RunAsync_WithValidData_ShouldReturnFilteredAndRankedSignals()
    {
        // Arrange
        var universe = new List<string> { "AAPL", "MSFT", "GOOGL", "TSLA", "NVDA" };
        _mockUniverseProvider.Setup(p => p.GetUniverseAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(universe);

        // Setup mock data for each symbol
        SetupMockDataForSymbol("AAPL", CreateBarsWithTrend(250, 150m, 0.15)); // 15% 6-month return
        SetupMockDataForSymbol("MSFT", CreateBarsWithTrend(250, 300m, 0.10)); // 10% 6-month return
        SetupMockDataForSymbol("GOOGL", CreateBarsWithTrend(250, 2500m, 0.20)); // 20% 6-month return
        SetupMockDataForSymbol("TSLA", CreateBarsWithTrend(250, 200m, 0.05)); // 5% 6-month return, high volatility
        SetupMockDataForSymbol("NVDA", CreateBarsWithTrend(250, 400m, 0.25)); // 25% 6-month return

        // Act
        var signals = await _signalGenerator.RunAsync(3);

        // Assert
        signals.Should().NotBeNull();
        signals.Should().HaveCount(3); // Top 3 requested

        // Should be ordered by six-month return descending
        signals[0].Symbol.Should().Be("NVDA"); // 25% return
        signals[1].Symbol.Should().Be("GOOGL"); // 20% return
        signals[2].Symbol.Should().Be("AAPL"); // 15% return

        // All signals should be Buy type
        signals.Should().AllSatisfy(s => s.Type.Should().Be(SignalType.Buy));

        // All signals should have ATR and Price populated
        signals.Should().AllSatisfy(s =>
        {
            s.Atr.Should().NotBeNull();
            s.Atr.Should().BeGreaterThan(0);
            s.Price.Should().NotBeNull();
            s.Price.Should().BeGreaterThan(0);
        });
    }

    [Fact]
    public async Task RunAsync_WithHighVolatilitySymbols_ShouldFilterOutHighVolatility()
    {
        // Arrange
        var universe = new List<string> { "STABLE", "VOLATILE" };
        _mockUniverseProvider.Setup(p => p.GetUniverseAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(universe);

        // STABLE: Low volatility, meets criteria
        SetupMockDataForSymbol("STABLE", CreateBarsWithTrend(250, 100m, 0.10, lowVolatility: true));
        
        // VOLATILE: High volatility (ATR/Close > 0.03), should be filtered out
        SetupMockDataForSymbol("VOLATILE", CreateBarsWithTrend(250, 100m, 0.15, lowVolatility: false));

        // Act
        var signals = await _signalGenerator.RunAsync(10);

        // Assert
        signals.Should().HaveCount(1);
        signals[0].Symbol.Should().Be("STABLE");
    }

    [Fact]
    public async Task RunAsync_WithBearishSymbols_ShouldFilterOutBearishTrends()
    {
        // Arrange
        var universe = new List<string> { "BULLISH", "BEARISH" };
        _mockUniverseProvider.Setup(p => p.GetUniverseAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(universe);

        // BULLISH: Price > SMA50 > SMA200
        SetupMockDataForSymbol("BULLISH", CreateBarsWithTrend(250, 100m, 0.10));
        
        // BEARISH: Price < SMA50 (bearish trend)
        SetupMockDataForSymbol("BEARISH", CreateBearishBars(250, 100m));

        // Act
        var signals = await _signalGenerator.RunAsync(10);

        // Assert
        signals.Should().HaveCount(1);
        signals[0].Symbol.Should().Be("BULLISH");
    }

    [Fact]
    public async Task RunAsync_WithInsufficientData_ShouldSkipSymbolsWithInsufficientBars()
    {
        // Arrange
        var universe = new List<string> { "GOOD", "INSUFFICIENT" };
        _mockUniverseProvider.Setup(p => p.GetUniverseAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(universe);

        SetupMockDataForSymbol("GOOD", CreateBarsWithTrend(250, 100m, 0.10));
        SetupMockDataForSymbol("INSUFFICIENT", CreateBarsWithTrend(100, 100m, 0.10)); // Only 100 bars

        // Act
        var signals = await _signalGenerator.RunAsync(10);

        // Assert
        signals.Should().HaveCount(1);
        signals[0].Symbol.Should().Be("GOOD");
    }

    [Fact]
    public async Task RunAsync_WithEmptyUniverse_ShouldReturnEmptyList()
    {
        // Arrange
        _mockUniverseProvider.Setup(p => p.GetUniverseAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<string>());

        // Act
        var signals = await _signalGenerator.RunAsync(10);

        // Assert
        signals.Should().BeEmpty();
    }

    [Fact]
    public async Task RunAsync_WithApiException_ShouldReturnEmptyListAndLogError()
    {
        // Arrange
        _mockUniverseProvider.Setup(p => p.GetUniverseAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("API Error"));

        // Act
        var signals = await _signalGenerator.RunAsync(10);

        // Assert
        signals.Should().BeEmpty();
    }

    private void SetupMockDataForSymbol(string symbol, List<IBar> bars)
    {
        var mockBarsPage = new Mock<IPage<IBar>>();
        mockBarsPage.Setup(p => p.Items).Returns(bars);

        _mockDataClient.Setup(c => c.ListHistoricalBarsAsync(
            It.Is<HistoricalBarsRequest>(r => r.Symbols.Contains(symbol)),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockBarsPage.Object);
    }

    private List<IBar> CreateBarsWithTrend(int count, decimal basePrice, double sixMonthReturn, bool lowVolatility = true)
    {
        var bars = new List<IBar>();
        var baseDate = DateTime.UtcNow.AddDays(-count);
        
        // Calculate starting price to achieve desired six-month return
        var startPrice = basePrice / (1m + (decimal)sixMonthReturn);
        var priceIncrement = (basePrice - startPrice) / count;

        for (int i = 0; i < count; i++)
        {
            var currentPrice = startPrice + (priceIncrement * i);
            var volatility = lowVolatility ? currentPrice * 0.01m : currentPrice * 0.05m; // 1% or 5% volatility

            var bar = new Mock<IBar>();
            bar.Setup(b => b.TimeUtc).Returns(baseDate.AddDays(i));
            bar.Setup(b => b.Open).Returns(currentPrice);
            bar.Setup(b => b.High).Returns(currentPrice + volatility);
            bar.Setup(b => b.Low).Returns(currentPrice - volatility);
            bar.Setup(b => b.Close).Returns(currentPrice);
            bar.Setup(b => b.Volume).Returns(1000000);
            bars.Add(bar.Object);
        }

        return bars;
    }

    private List<IBar> CreateBearishBars(int count, decimal basePrice)
    {
        var bars = new List<IBar>();
        var baseDate = DateTime.UtcNow.AddDays(-count);

        for (int i = 0; i < count; i++)
        {
            // Create declining price trend
            var currentPrice = basePrice * (1m - (decimal)i / count * 0.3m); // 30% decline over period
            var volatility = currentPrice * 0.01m;

            var bar = new Mock<IBar>();
            bar.Setup(b => b.TimeUtc).Returns(baseDate.AddDays(i));
            bar.Setup(b => b.Open).Returns(currentPrice);
            bar.Setup(b => b.High).Returns(currentPrice + volatility);
            bar.Setup(b => b.Low).Returns(currentPrice - volatility);
            bar.Setup(b => b.Close).Returns(currentPrice);
            bar.Setup(b => b.Volume).Returns(1000000);
            bars.Add(bar.Object);
        }

        return bars;
    }
}
