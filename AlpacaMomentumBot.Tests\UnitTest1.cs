using AlpacaMomentumBot.Console.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace AlpacaMomentumBot.Tests;

public class RiskManagerTests
{
    private readonly Mock<ILogger<RiskManager>> _loggerMock;
    private readonly Mock<IAlpacaClientFactory> _clientFactoryMock;
    private readonly Mock<IAlpacaTradingClient> _tradingClientMock;
    private readonly Mock<IAccount> _accountMock;
    private readonly RiskManager _riskManager;

    public RiskManagerTests()
    {
        _loggerMock = new Mock<ILogger<RiskManager>>();
        _clientFactoryMock = new Mock<IAlpacaClientFactory>();
        _tradingClientMock = new Mock<IAlpacaTradingClient>();
        _accountMock = new Mock<IAccount>();

        _clientFactoryMock.Setup(x => x.CreateTradingClient()).Returns(_tradingClientMock.Object);
        _tradingClientMock.Setup(x => x.GetAccountAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(_accountMock.Object);

        _riskManager = new RiskManager(_loggerMock.Object, _clientFactoryMock.Object);
    }

    [Fact]
    public async Task AssessRiskAsync_WithNoSignal_ShouldRejectTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.None, 0, DateTime.UtcNow, "No signal");
        var portfolioValue = 10000m;

        // Act
        var result = await _riskManager.AssessRiskAsync(signal, portfolioValue);

        // Assert
        result.IsApproved.Should().BeFalse();
        result.RiskReason.Should().Be("No trading signal");
    }

    [Fact]
    public async Task AssessRiskAsync_WithLowConfidence_ShouldRejectTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.2m, DateTime.UtcNow, "Low confidence");
        var portfolioValue = 10000m;

        // Act
        var result = await _riskManager.AssessRiskAsync(signal, portfolioValue);

        // Assert
        result.IsApproved.Should().BeFalse();
        result.RiskReason.Should().Contain("Signal confidence too low");
    }

    [Fact]
    public async Task AssessRiskAsync_WithValidSignal_ShouldApproveTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.8m, DateTime.UtcNow, "Strong buy signal");
        var portfolioValue = 10000m;

        // Act
        var result = await _riskManager.AssessRiskAsync(signal, portfolioValue);

        // Assert
        result.IsApproved.Should().BeTrue();
        result.MaxPositionSize.Should().BeGreaterThan(0);
        result.StopLossPrice.Should().BeGreaterThan(0);
        result.TakeProfitPrice.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task AssessRiskAsync_WithZeroPortfolioValue_ShouldRejectTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.8m, DateTime.UtcNow, "Strong buy signal");
        var portfolioValue = 0m;

        // Act
        var result = await _riskManager.AssessRiskAsync(signal, portfolioValue);

        // Assert
        result.IsApproved.Should().BeFalse();
        result.RiskReason.Should().Be("Invalid portfolio value");
    }

    [Fact]
    public async Task CalculateQuantityAsync_WithValidInputs_ShouldCalculateCorrectQuantity()
    {
        // Arrange
        var price = 100m;
        var atr14 = 2.0; // $2 ATR
        var accountEquity = 100000m; // $100k account

        _accountMock.Setup(x => x.Equity).Returns(accountEquity);

        // Act
        var result = await _riskManager.CalculateQuantityAsync(price, atr14, CancellationToken.None);

        // Expected: riskDollars = min(100000 * 0.01, 1000) = 1000
        // Expected: quantity = 1000 / (2.0 * 100) = 5.0
        var expectedQuantity = 5.0m;

        // Assert
        result.Should().Be(expectedQuantity);
    }

    [Fact]
    public async Task CalculateQuantityAsync_WithSmallAccount_ShouldUseEquityBasedRisk()
    {
        // Arrange
        var price = 50m;
        var atr14 = 1.0; // $1 ATR
        var accountEquity = 10000m; // $10k account (smaller than $100k cap)

        _accountMock.Setup(x => x.Equity).Returns(accountEquity);

        // Act
        var result = await _riskManager.CalculateQuantityAsync(price, atr14, CancellationToken.None);

        // Expected: riskDollars = min(10000 * 0.01, 1000) = 100
        // Expected: quantity = 100 / (1.0 * 50) = 2.0
        var expectedQuantity = 2.0m;

        // Assert
        result.Should().Be(expectedQuantity);
    }

    [Fact]
    public async Task CalculateQuantityAsync_ShouldRespectRiskTolerance()
    {
        // Arrange
        var price = 100m;
        var atr14 = 0.1; // Very low ATR that would create high quantity
        var accountEquity = 100000m;

        _accountMock.Setup(x => x.Equity).Returns(accountEquity);

        // Act
        var result = await _riskManager.CalculateQuantityAsync(price, atr14, CancellationToken.None);

        // Expected: riskDollars = min(100000 * 0.01, 1000) = 1000
        // Calculated: quantity = 1000 / (0.1 * 100) = 100
        // Max by risk tolerance: 1000 / 100 = 10
        // Should be capped at 10
        var maxQuantityByRisk = 10m;

        // Assert
        result.Should().BeLessThanOrEqualTo(maxQuantityByRisk);
        result.Should().Be(maxQuantityByRisk);
    }

    [Fact]
    public async Task CalculateQuantityAsync_WithZeroPrice_ShouldReturnZero()
    {
        // Arrange
        var price = 0m;
        var atr14 = 2.0;
        var accountEquity = 100000m;

        _accountMock.Setup(x => x.Equity).Returns(accountEquity);

        // Act
        var result = await _riskManager.CalculateQuantityAsync(price, atr14, CancellationToken.None);

        // Assert
        result.Should().Be(0m);
    }

    [Fact]
    public async Task CalculateQuantityAsync_WithZeroAtr_ShouldReturnZero()
    {
        // Arrange
        var price = 100m;
        var atr14 = 0.0;
        var accountEquity = 100000m;

        _accountMock.Setup(x => x.Equity).Returns(accountEquity);

        // Act
        var result = await _riskManager.CalculateQuantityAsync(price, atr14, CancellationToken.None);

        // Assert
        result.Should().Be(0m);
    }

    [Fact]
    public async Task CalculateQuantityAsync_WithZeroEquity_ShouldReturnZero()
    {
        // Arrange
        var price = 100m;
        var atr14 = 2.0;

        _accountMock.Setup(x => x.Equity).Returns((decimal?)null);

        // Act
        var result = await _riskManager.CalculateQuantityAsync(price, atr14, CancellationToken.None);

        // Assert
        result.Should().Be(0m);
    }

    [Fact]
    public async Task CalculateQuantityAsync_WithNegativeEquity_ShouldReturnZero()
    {
        // Arrange
        var price = 100m;
        var atr14 = 2.0;

        _accountMock.Setup(x => x.Equity).Returns(-1000m);

        // Act
        var result = await _riskManager.CalculateQuantityAsync(price, atr14, CancellationToken.None);

        // Assert
        result.Should().Be(0m);
    }

    [Fact]
    public async Task AssessRiskAsync_WithSignalContainingPriceAndAtr_ShouldUseCalculateQuantity()
    {
        // Arrange
        var price = 150m;
        var atr14 = 3.0;
        var accountEquity = 50000m; // $50k account
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.8m, DateTime.UtcNow, "Strong buy signal", atr14, price);
        var portfolioValue = 50000m;

        _accountMock.Setup(x => x.Equity).Returns(accountEquity);

        // Act
        var result = await _riskManager.AssessRiskAsync(signal, portfolioValue);

        // Expected: riskDollars = min(50000 * 0.01, 1000) = 500
        // Expected: quantity = 500 / (3.0 * 150) = 1.111...
        var expectedQuantity = 500m / (3.0m * 150m);

        // Assert
        result.IsApproved.Should().BeTrue();
        result.MaxPositionSize.Should().BeApproximately(expectedQuantity, 0.001m);
        result.StopLossPrice.Should().Be(price * 0.98m); // 2% stop loss
        result.TakeProfitPrice.Should().Be(price * 1.06m); // 6% take profit
    }

    [Fact]
    public async Task AssessRiskAsync_WithSignalMissingPriceOrAtr_ShouldUseFallbackSizing()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.8m, DateTime.UtcNow, "Strong buy signal"); // No price/ATR
        var portfolioValue = 50000m;

        // Act
        var result = await _riskManager.AssessRiskAsync(signal, portfolioValue);

        // Expected: fallback to 5% of portfolio / $100 estimated price = 2500 / 100 = 25 shares
        var expectedQuantity = 25m;

        // Assert
        result.IsApproved.Should().BeTrue();
        result.MaxPositionSize.Should().Be(expectedQuantity);
    }
}